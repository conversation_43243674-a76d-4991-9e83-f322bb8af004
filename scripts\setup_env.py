#!/usr/bin/env python3
"""
Environment setup helper script.
This script helps you configure the environment variables for PostgreSQL.
"""

import os
from pathlib import Path

def create_env_file():
    """Create a .env.local file with PostgreSQL configuration."""
    
    print("🔧 PostgreSQL Environment Setup")
    print("=" * 50)
    print("This script will help you create a .env.local file with PostgreSQL configuration.")
    print()
    
    # Check if .env.local already exists
    env_file = Path(".env.local")
    if env_file.exists():
        response = input("⚠️  .env.local already exists. Overwrite? (y/N): ")
        if response.lower() != 'y':
            print("Setup cancelled.")
            return
    
    print("Please provide your PostgreSQL connection details:")
    print("(Press Enter for default values shown in brackets)")
    print()
    
    # Get PostgreSQL configuration
    host = input("PostgreSQL Host [localhost]: ").strip() or "localhost"
    port = input("PostgreSQL Port [5432]: ").strip() or "5432"
    database = input("Database Name [ocr_document_grossiste]: ").strip() or "ocr_document_grossiste"
    user = input("PostgreSQL User [postgres]: ").strip() or "postgres"
    password = input("PostgreSQL Password: ").strip()
    
    if not password:
        print("⚠️  Warning: No password provided. This might cause connection issues.")
    
    # SSL Mode
    print("\nSSL Mode options:")
    print("1. require (for cloud databases)")
    print("2. prefer (for VPS/local with SSL)")
    print("3. disable (for local development only)")
    ssl_choice = input("Choose SSL mode [2]: ").strip() or "2"
    
    ssl_modes = {"1": "require", "2": "prefer", "3": "disable"}
    ssl_mode = ssl_modes.get(ssl_choice, "prefer")
    
    # Database strategy
    print("\nDatabase Strategy:")
    print("1. Dual database (PostgreSQL + SQLite) - Recommended")
    print("2. PostgreSQL only")
    print("3. SQLite only (fallback)")
    strategy_choice = input("Choose strategy [1]: ").strip() or "1"
    
    if strategy_choice == "1":
        use_dual = "True"
        primary = "postgresql"
    elif strategy_choice == "2":
        use_dual = "False"
        primary = "postgresql"
    else:
        use_dual = "False"
        primary = "sqlite"
    
    # Create .env.local content
    env_content = f"""# OCR Document Grossiste - Environment Configuration
# Generated by setup_env.py

# Application Environment
ENVIRONMENT=local
DEBUG=True

# Tesseract OCR Configuration
TESSERACT_PATH=C:\\Program Files\\Tesseract-OCR\\tesseract.exe

# API Configuration
API_URL=http://localhost:8088
TAP_URL=http://your-tap-system-url

# WinPlus ERP Integration
WINPLUS_AUTH_USER=your_winplus_user
WINPLUS_AUTH_TENANT=your_winplus_tenant
WINPLUS_URL=http://your-winplus-url

# JWT Authentication
SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# PostgreSQL Database Configuration
POSTGRES_HOST={host}
POSTGRES_PORT={port}
POSTGRES_DB={database}
POSTGRES_USER={user}
POSTGRES_PASSWORD={password}
POSTGRES_SSL_MODE={ssl_mode}

# Database Strategy Configuration
USE_DUAL_DATABASE={use_dual}
PRIMARY_DATABASE={primary}
"""
    
    # Write the file
    try:
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print(f"\n✅ Environment file created: {env_file}")
        print("\n📋 Configuration Summary:")
        print(f"   PostgreSQL: {user}@{host}:{port}/{database}")
        print(f"   SSL Mode: {ssl_mode}")
        print(f"   Strategy: {'Dual Database' if use_dual == 'True' else primary.title() + ' Only'}")
        
        print("\n🚀 Next Steps:")
        print("1. Test the connection: python scripts/test_connection.py")
        print("2. Initialize PostgreSQL: python scripts/init_postgresql.py")
        print("3. Start the application: uvicorn src.api:app --host 0.0.0.0 --port 8088")
        
        if host == "localhost" and strategy_choice in ["1", "2"]:
            print("\n💡 For local development, you can start PostgreSQL with:")
            print("   docker-compose -f docker-compose.dev.yml up -d")
        
    except Exception as e:
        print(f"❌ Error creating environment file: {e}")

def main():
    create_env_file()

if __name__ == "__main__":
    main()
