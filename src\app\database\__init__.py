"""
Database package initialization.
This module provides easy access to database operations and models.
"""

from .models import PreBlOcr, ProcessingLog, SupplierConfig, Base
from .connection import (
    db_manager, 
    init_db, 
    get_postgres_session, 
    get_sqlite_session,
    get_db_connection
)
from .dual_operations import dual_db

__all__ = [
    'PreBlOcr',
    'ProcessingLog', 
    'SupplierConfig',
    'Base',
    'db_manager',
    'init_db',
    'get_postgres_session',
    'get_sqlite_session', 
    'get_db_connection',
    'dual_db'
]
