"""
Database connection management for both PostgreSQL and SQLite.
This module handles database connections, session management, and initialization.
"""

import logging
from contextlib import contextmanager
from typing import Generator, Optional
import sqlite3
import json
from pathlib import Path

from sqlalchemy import create_engine, text, MetaData
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError
from sqlalchemy.pool import StaticPool

from src.app.config import (
    POSTGRES_URL, SQLITE_DB_PATH, SQLITE_DB_DIR,
    USE_DUAL_DATABASE, PRIMARY_DATABASE
)
from src.app.database.models import Base, PreBlOcr

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages connections to both PostgreSQL and SQLite databases.
    Provides unified interface for database operations.
    """
    
    def __init__(self):
        self.postgres_engine = None
        self.postgres_session_factory = None
        self.sqlite_engine = None
        self.sqlite_session_factory = None
        self._initialize_databases()
    
    def _initialize_databases(self):
        """Initialize database connections and session factories."""
        try:
            # Initialize PostgreSQL
            self._initialize_postgresql()
            
            # Initialize SQLite
            self._initialize_sqlite()
            
            logger.info("Database connections initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize databases: {e}")
            raise
    
    def _initialize_postgresql(self):
        """Initialize PostgreSQL connection."""
        try:
            # Create PostgreSQL engine with connection pooling
            self.postgres_engine = create_engine(
                POSTGRES_URL,
                pool_size=10,
                max_overflow=20,
                pool_pre_ping=True,
                pool_recycle=3600,
                echo=False  # Set to True for SQL debugging
            )
            
            # Test connection
            with self.postgres_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            # Create session factory
            self.postgres_session_factory = sessionmaker(
                bind=self.postgres_engine,
                autocommit=False,
                autoflush=False
            )
            
            logger.info("PostgreSQL connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize PostgreSQL: {e}")
            if PRIMARY_DATABASE == 'postgresql':
                raise
            else:
                logger.warning("PostgreSQL unavailable, continuing with SQLite only")
    
    def _initialize_sqlite(self):
        """Initialize SQLite connection."""
        try:
            # Ensure SQLite directory exists
            SQLITE_DB_DIR.mkdir(parents=True, exist_ok=True)
            
            # Create SQLite engine
            self.sqlite_engine = create_engine(
                f"sqlite:///{SQLITE_DB_PATH}",
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 30
                },
                echo=False  # Set to True for SQL debugging
            )
            
            # Create session factory
            self.sqlite_session_factory = sessionmaker(
                bind=self.sqlite_engine,
                autocommit=False,
                autoflush=False
            )
            
            logger.info("SQLite connection initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize SQLite: {e}")
            raise
    
    def create_tables(self):
        """Create tables in both databases."""
        # Create PostgreSQL tables
        if self.postgres_engine:
            try:
                Base.metadata.create_all(bind=self.postgres_engine)
                logger.info("PostgreSQL tables created successfully")
            except Exception as e:
                logger.error(f"Failed to create PostgreSQL tables: {e}")
                if PRIMARY_DATABASE == 'postgresql':
                    raise
        
        # Create SQLite tables (using existing init_db logic)
        if self.sqlite_engine:
            try:
                self._create_sqlite_tables()
                logger.info("SQLite tables created successfully")
            except Exception as e:
                logger.error(f"Failed to create SQLite tables: {e}")
                if PRIMARY_DATABASE == 'sqlite':
                    raise
    
    def _create_sqlite_tables(self):
        """Create SQLite tables using raw SQL (maintains compatibility)."""
        conn = sqlite3.connect(SQLITE_DB_PATH)
        c = conn.cursor()
        try:
            c.execute('''CREATE TABLE IF NOT EXISTS pre_bl_ocr
                         (ID_BL INTEGER PRIMARY KEY AUTOINCREMENT,
                          Content JSON,
                          ID_USER TEXT,
                          status TEXT DEFAULT 'EN_ATTENTE',
                          ID_TENANT TEXT,
                          CODE_TENANT TEXT,
                          date TEXT DEFAULT CURRENT_TIMESTAMP,
                          id_BL_origine TEXT DEFAULT NULL,
                          date_BL_origine TEXT DEFAULT NULL,
                          supplier_name TEXT DEFAULT NULL,
                          supplier_id TEXT DEFAULT NULL, 
                          random_id TEXT DEFAULT NULL 
                          )''')
            conn.commit()
        finally:
            conn.close()
    
    @contextmanager
    def get_postgres_session(self) -> Generator[Session, None, None]:
        """Get PostgreSQL session with automatic cleanup."""
        if not self.postgres_session_factory:
            raise RuntimeError("PostgreSQL not available")
        
        session = self.postgres_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"PostgreSQL session error: {e}")
            raise
        finally:
            session.close()
    
    @contextmanager
    def get_sqlite_session(self) -> Generator[Session, None, None]:
        """Get SQLite session with automatic cleanup."""
        if not self.sqlite_session_factory:
            raise RuntimeError("SQLite not available")
        
        session = self.sqlite_session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"SQLite session error: {e}")
            raise
        finally:
            session.close()
    
    def get_sqlite_connection(self):
        """Get raw SQLite connection (for compatibility with existing code)."""
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    
    def health_check(self) -> dict:
        """Check the health of both database connections."""
        status = {
            'postgresql': {'available': False, 'error': None},
            'sqlite': {'available': False, 'error': None}
        }
        
        # Check PostgreSQL
        if self.postgres_engine:
            try:
                with self.postgres_engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                status['postgresql']['available'] = True
            except Exception as e:
                status['postgresql']['error'] = str(e)
        
        # Check SQLite
        if self.sqlite_engine:
            try:
                with self.sqlite_engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                status['sqlite']['available'] = True
            except Exception as e:
                status['sqlite']['error'] = str(e)
        
        return status


# Global database manager instance
db_manager = DatabaseManager()


# Convenience functions for backward compatibility
def get_db_connection():
    """Get SQLite connection (maintains compatibility with existing code)."""
    return db_manager.get_sqlite_connection()


def init_db():
    """Initialize databases and create tables."""
    db_manager.create_tables()


# Context managers for easy session management
@contextmanager
def get_postgres_session() -> Generator[Session, None, None]:
    """Get PostgreSQL session."""
    with db_manager.get_postgres_session() as session:
        yield session


@contextmanager
def get_sqlite_session() -> Generator[Session, None, None]:
    """Get SQLite session."""
    with db_manager.get_sqlite_session() as session:
        yield session
