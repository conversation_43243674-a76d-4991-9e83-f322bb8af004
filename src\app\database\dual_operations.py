"""
Dual database operations layer.
This module provides operations that work with both PostgreSQL and SQLite databases,
ensuring data redundancy and fallback capabilities.
"""

import logging
import json
import sqlite3
from typing import List, Dict, Optional, Any
from datetime import datetime

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, desc

from src.app.config import USE_DUAL_DATABASE, PRIMARY_DATABASE
from src.app.database.connection import (
    db_manager, get_postgres_session, get_sqlite_session
)
from src.app.database.models import PreBlOcr

logger = logging.getLogger(__name__)


class DualDatabaseOperations:
    """
    Handles operations across both PostgreSQL and SQLite databases.
    Provides data redundancy and fallback mechanisms.
    """
    
    def __init__(self):
        self.use_dual = USE_DUAL_DATABASE
        self.primary_db = PRIMARY_DATABASE
    
    def save_response_to_db(self, responses: List[Dict], id_user: str, 
                          tenant_code: str, tenant_id: str, random_id: str) -> int:
        """
        Save OCR response data to both databases.
        Returns the ID of the saved record.
        """
        # Extract only the 'data' part from each response
        data_to_save = [response['data'] for response in responses 
                       if response.get('success') and response.get('data')]
        
        # Prepare the record data
        record_data = {
            'Content': data_to_save,
            'ID_USER': id_user,
            'ID_TENANT': tenant_id,
            'CODE_TENANT': tenant_code,
            'random_id': random_id,
            'status': 'EN_ATTENTE',
            'date': datetime.now()
        }
        
        primary_id = None
        backup_success = False
        
        try:
            # Save to primary database
            if self.primary_db == 'postgresql':
                primary_id = self._save_to_postgresql(record_data)
                logger.info(f"Saved to PostgreSQL with ID: {primary_id}")
            else:
                primary_id = self._save_to_sqlite_raw(record_data)
                logger.info(f"Saved to SQLite with ID: {primary_id}")
            
            # Save to backup database if dual mode is enabled
            if self.use_dual:
                try:
                    if self.primary_db == 'postgresql':
                        self._save_to_sqlite_raw(record_data)
                        backup_success = True
                        logger.info("Backup saved to SQLite successfully")
                    else:
                        backup_id = self._save_to_postgresql(record_data)
                        backup_success = True
                        logger.info(f"Backup saved to PostgreSQL with ID: {backup_id}")
                except Exception as e:
                    logger.warning(f"Failed to save backup: {e}")
                    # Continue execution - backup failure shouldn't stop the process
            
            return primary_id
            
        except Exception as e:
            logger.error(f"Failed to save to primary database: {e}")
            
            # Try fallback to backup database
            if self.use_dual:
                try:
                    if self.primary_db == 'postgresql':
                        fallback_id = self._save_to_sqlite_raw(record_data)
                        logger.warning(f"Fallback: Saved to SQLite with ID: {fallback_id}")
                        return fallback_id
                    else:
                        fallback_id = self._save_to_postgresql(record_data)
                        logger.warning(f"Fallback: Saved to PostgreSQL with ID: {fallback_id}")
                        return fallback_id
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
            
            raise Exception(f"Failed to save to any database: {e}")
    
    def _save_to_postgresql(self, record_data: Dict) -> int:
        """Save record to PostgreSQL database."""
        try:
            with get_postgres_session() as session:
                record = PreBlOcr.from_dict(record_data)
                session.add(record)
                session.flush()  # Get the ID without committing
                record_id = record.ID_BL
                session.commit()
                return record_id
        except Exception as e:
            logger.error(f"PostgreSQL save error: {e}")
            raise
    
    def _save_to_sqlite_raw(self, record_data: Dict) -> int:
        """Save record to SQLite database using raw SQL (maintains compatibility)."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("BEGIN TRANSACTION")
            c.execute("""INSERT INTO pre_bl_ocr 
                        (Content, ID_USER, ID_TENANT, CODE_TENANT, random_id) 
                        VALUES (json(?), ?, ?, ?, ?)""",
                     (json.dumps(record_data['Content']), 
                      record_data['ID_USER'],
                      record_data['ID_TENANT'], 
                      record_data['CODE_TENANT'], 
                      record_data['random_id']))
            c.execute("SELECT last_insert_rowid()")
            record_id = c.fetchone()[0]
            c.execute("COMMIT")
            return record_id
        except Exception as e:
            c.execute("ROLLBACK")
            logger.error(f"SQLite save error: {e}")
            raise
        finally:
            conn.close()
    
    def get_all_pre_bl_ocr(self, user_id: str, tenant_id: str) -> List[Dict]:
        """Get all pre_bl_ocr records for a user and tenant."""
        try:
            # Try primary database first
            if self.primary_db == 'postgresql':
                return self._get_all_from_postgresql(user_id, tenant_id)
            else:
                return self._get_all_from_sqlite(user_id, tenant_id)
        except Exception as e:
            logger.error(f"Failed to get records from primary database: {e}")
            
            # Try fallback database
            if self.use_dual:
                try:
                    if self.primary_db == 'postgresql':
                        logger.warning("Falling back to SQLite")
                        return self._get_all_from_sqlite(user_id, tenant_id)
                    else:
                        logger.warning("Falling back to PostgreSQL")
                        return self._get_all_from_postgresql(user_id, tenant_id)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
            
            raise Exception(f"Failed to retrieve records from any database: {e}")
    
    def _get_all_from_postgresql(self, user_id: str, tenant_id: str) -> List[Dict]:
        """Get all records from PostgreSQL."""
        with get_postgres_session() as session:
            records = session.query(PreBlOcr).filter(
                and_(
                    PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                    PreBlOcr.ID_USER == user_id,
                    PreBlOcr.ID_TENANT == tenant_id
                )
            ).order_by(desc(PreBlOcr.date)).all()
            
            return [record.to_dict() for record in records]
    
    def _get_all_from_sqlite(self, user_id: str, tenant_id: str) -> List[Dict]:
        """Get all records from SQLite."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("""
                SELECT * 
                FROM pre_bl_ocr 
                WHERE status in ('EN_COURS', 'VALIDER') 
                  AND ID_USER = ? 
                  AND ID_TENANT = ? 
                ORDER BY date DESC
            """, (user_id, tenant_id))
            rows = c.fetchall()
            
            results = []
            for row in rows:
                results.append({
                    "ID_BL": row['ID_BL'],
                    "Content": json.loads(row['Content']) if row['Content'] else None,
                    "ID_USER": row['ID_USER'],
                    "status": row['status'],
                    "ID_TENANT": row['ID_TENANT'],
                    "CODE_TENANT": row['CODE_TENANT'],
                    "date": row['date'],
                    "id_BL_origine": row['id_BL_origine'],
                    "date_BL_origine": row['date_BL_origine'],
                    "supplier_name": row['supplier_name'],
                    "supplier_id": str(row['supplier_id']) if row['supplier_id'] else None,
                    "random_id": row['random_id']
                })
            return results
        finally:
            conn.close()
    
    def get_pre_bl_ocr_by_id(self, id_bl: int, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get a specific pre_bl_ocr record by ID."""
        try:
            # Try primary database first
            if self.primary_db == 'postgresql':
                return self._get_by_id_from_postgresql(id_bl, user_id, tenant_id)
            else:
                return self._get_by_id_from_sqlite(id_bl, user_id, tenant_id)
        except Exception as e:
            logger.error(f"Failed to get record from primary database: {e}")
            
            # Try fallback database
            if self.use_dual:
                try:
                    if self.primary_db == 'postgresql':
                        return self._get_by_id_from_sqlite(id_bl, user_id, tenant_id)
                    else:
                        return self._get_by_id_from_postgresql(id_bl, user_id, tenant_id)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")
            
            return None

    def _get_by_id_from_postgresql(self, id_bl: int, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get record by ID from PostgreSQL."""
        with get_postgres_session() as session:
            record = session.query(PreBlOcr).filter(
                and_(
                    PreBlOcr.ID_BL == id_bl,
                    PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                    PreBlOcr.ID_USER == user_id,
                    PreBlOcr.ID_TENANT == tenant_id
                )
            ).first()

            return record.to_dict() if record else None

    def _get_by_id_from_sqlite(self, id_bl: int, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get record by ID from SQLite."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("""SELECT * FROM pre_bl_ocr
                        WHERE ID_BL = ? AND status in ('EN_COURS', 'VALIDER')
                        AND ID_USER = ? AND ID_TENANT = ?""",
                     (id_bl, user_id, tenant_id))
            row = c.fetchone()

            if row:
                return {
                    "ID_BL": row['ID_BL'],
                    "Content": json.loads(row['Content']) if row['Content'] else None,
                    "ID_USER": row['ID_USER'],
                    "status": row['status'],
                    "ID_TENANT": row['ID_TENANT'],
                    "CODE_TENANT": row['CODE_TENANT'],
                    "date": row['date'],
                    "id_BL_origine": row['id_BL_origine'],
                    "date_BL_origine": row['date_BL_origine'],
                    "supplier_name": row['supplier_name'],
                    "supplier_id": str(row['supplier_id']) if row['supplier_id'] else None,
                    "random_id": row['random_id']
                }
            return None
        finally:
            conn.close()

    def get_pre_bl_ocr_by_random_id(self, random_id: str, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get a specific pre_bl_ocr record by random_id."""
        try:
            # Try primary database first
            if self.primary_db == 'postgresql':
                return self._get_by_random_id_from_postgresql(random_id, user_id, tenant_id)
            else:
                return self._get_by_random_id_from_sqlite(random_id, user_id, tenant_id)
        except Exception as e:
            logger.error(f"Failed to get record by random_id from primary database: {e}")

            # Try fallback database
            if self.use_dual:
                try:
                    if self.primary_db == 'postgresql':
                        return self._get_by_random_id_from_sqlite(random_id, user_id, tenant_id)
                    else:
                        return self._get_by_random_id_from_postgresql(random_id, user_id, tenant_id)
                except Exception as fallback_error:
                    logger.error(f"Fallback also failed: {fallback_error}")

            return None

    def _get_by_random_id_from_postgresql(self, random_id: str, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get record by random_id from PostgreSQL."""
        with get_postgres_session() as session:
            record = session.query(PreBlOcr).filter(
                and_(
                    PreBlOcr.random_id == random_id,
                    PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                    PreBlOcr.ID_USER == user_id,
                    PreBlOcr.ID_TENANT == tenant_id
                )
            ).first()

            return record.to_dict() if record else None

    def _get_by_random_id_from_sqlite(self, random_id: str, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get record by random_id from SQLite."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("""SELECT * FROM pre_bl_ocr
                        WHERE random_id = ? AND status in ('EN_COURS', 'VALIDER')
                        AND ID_USER = ? AND ID_TENANT = ?""",
                     (random_id, user_id, tenant_id))
            row = c.fetchone()

            if row:
                return {
                    "ID_BL": row['ID_BL'],
                    "Content": json.loads(row['Content']) if row['Content'] else None,
                    "ID_USER": row['ID_USER'],
                    "status": row['status'],
                    "ID_TENANT": row['ID_TENANT'],
                    "CODE_TENANT": row['CODE_TENANT'],
                    "date": row['date'],
                    "id_BL_origine": row['id_BL_origine'],
                    "date_BL_origine": row['date_BL_origine'],
                    "supplier_name": row['supplier_name'],
                    "supplier_id": str(row['supplier_id']) if row['supplier_id'] else None,
                    "random_id": row['random_id']
                }
            return None
        finally:
            conn.close()

    def update_bl_status(self, bl_id: int, new_status: str, id_BL_origine: str,
                        date_BL_origine: str, supplier_name: str, supplier_id: str) -> bool:
        """Update BL status in both databases."""
        primary_success = False
        backup_success = False

        try:
            # Update primary database
            if self.primary_db == 'postgresql':
                primary_success = self._update_status_postgresql(
                    bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)
            else:
                primary_success = self._update_status_sqlite(
                    bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)

            # Update backup database if dual mode is enabled
            if self.use_dual and primary_success:
                try:
                    if self.primary_db == 'postgresql':
                        backup_success = self._update_status_sqlite(
                            bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)
                    else:
                        backup_success = self._update_status_postgresql(
                            bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)
                except Exception as e:
                    logger.warning(f"Failed to update backup database: {e}")

            return primary_success

        except Exception as e:
            logger.error(f"Failed to update primary database: {e}")
            return False

    def _update_status_postgresql(self, bl_id: int, new_status: str, id_BL_origine: str,
                                 date_BL_origine: str, supplier_name: str, supplier_id: str) -> bool:
        """Update status in PostgreSQL."""
        try:
            with get_postgres_session() as session:
                record = session.query(PreBlOcr).filter(PreBlOcr.ID_BL == bl_id).first()
                if record:
                    record.status = new_status
                    record.id_BL_origine = id_BL_origine
                    record.date_BL_origine = date_BL_origine
                    record.supplier_name = supplier_name
                    record.supplier_id = supplier_id
                    session.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"PostgreSQL update error: {e}")
            return False

    def _update_status_sqlite(self, bl_id: int, new_status: str, id_BL_origine: str,
                             date_BL_origine: str, supplier_name: str, supplier_id: str) -> bool:
        """Update status in SQLite."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("""UPDATE pre_bl_ocr
                        SET status = ?, id_BL_origine = ?, date_BL_origine = ?,
                            supplier_name = ?, supplier_id = ?
                        WHERE ID_BL = ?""",
                     (new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id, bl_id))
            conn.commit()
            return c.rowcount > 0
        except Exception as e:
            logger.error(f"SQLite update error: {e}")
            return False
        finally:
            conn.close()

    def update_bl_status_valider(self, bl_id: int, user_id: str, tenant_id: str) -> bool:
        """Update BL status to 'VALIDER' in both databases."""
        primary_success = False

        try:
            # Update primary database
            if self.primary_db == 'postgresql':
                primary_success = self._update_status_valider_postgresql(bl_id, user_id, tenant_id)
            else:
                primary_success = self._update_status_valider_sqlite(bl_id, user_id, tenant_id)

            # Update backup database if dual mode is enabled
            if self.use_dual and primary_success:
                try:
                    if self.primary_db == 'postgresql':
                        self._update_status_valider_sqlite(bl_id, user_id, tenant_id)
                    else:
                        self._update_status_valider_postgresql(bl_id, user_id, tenant_id)
                except Exception as e:
                    logger.warning(f"Failed to update backup database: {e}")

            return primary_success

        except Exception as e:
            logger.error(f"Failed to update status to VALIDER: {e}")
            return False

    def _update_status_valider_postgresql(self, bl_id: int, user_id: str, tenant_id: str) -> bool:
        """Update status to VALIDER in PostgreSQL."""
        try:
            with get_postgres_session() as session:
                record = session.query(PreBlOcr).filter(
                    and_(
                        PreBlOcr.ID_BL == bl_id,
                        PreBlOcr.ID_USER == user_id,
                        PreBlOcr.ID_TENANT == tenant_id
                    )
                ).first()
                if record:
                    record.status = 'VALIDER'
                    session.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"PostgreSQL update VALIDER error: {e}")
            return False

    def _update_status_valider_sqlite(self, bl_id: int, user_id: str, tenant_id: str) -> bool:
        """Update status to VALIDER in SQLite."""
        conn = db_manager.get_sqlite_connection()
        c = conn.cursor()
        try:
            c.execute("""UPDATE pre_bl_ocr SET status = 'VALIDER'
                        WHERE ID_BL = ? AND ID_USER = ? AND ID_TENANT = ?""",
                     (bl_id, user_id, tenant_id))
            conn.commit()
            return c.rowcount > 0
        except Exception as e:
            logger.error(f"SQLite update VALIDER error: {e}")
            return False
        finally:
            conn.close()

    def get_database_health(self) -> Dict[str, Any]:
        """Get health status of both databases."""
        return db_manager.health_check()


# Global instance for easy access
dual_db = DualDatabaseOperations()
