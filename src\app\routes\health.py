"""
Health check endpoints for monitoring database and system status.
"""

import logging
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
from datetime import datetime

from src.app.utils.db_operations import get_database_health
from src.app.config import PRIMARY_DATABASE, USE_DUAL_DATABASE

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/health")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    Returns overall system health status.
    """
    try:
        db_health = get_database_health()
        
        # Determine overall health
        primary_healthy = db_health.get(PRIMARY_DATABASE, {}).get('available', False)
        backup_healthy = True  # SQLite is usually always available
        
        if USE_DUAL_DATABASE:
            backup_db = 'sqlite' if PRIMARY_DATABASE == 'postgresql' else 'postgresql'
            backup_healthy = db_health.get(backup_db, {}).get('available', False)
        
        overall_healthy = primary_healthy or (USE_DUAL_DATABASE and backup_healthy)
        
        return {
            "status": "healthy" if overall_healthy else "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "database": {
                "primary": PRIMARY_DATABASE,
                "dual_mode": USE_DUAL_DATABASE,
                "status": "healthy" if primary_healthy else "degraded" if backup_healthy else "unhealthy"
            }
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")


@router.get("/health/database")
async def database_health() -> Dict[str, Any]:
    """
    Detailed database health check endpoint.
    Returns status of both PostgreSQL and SQLite databases.
    """
    try:
        db_health = get_database_health()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "configuration": {
                "primary_database": PRIMARY_DATABASE,
                "dual_database_enabled": USE_DUAL_DATABASE
            },
            "databases": db_health,
            "summary": {
                "total_databases": 2 if USE_DUAL_DATABASE else 1,
                "healthy_databases": sum(1 for db in db_health.values() if db.get('available', False)),
                "primary_healthy": db_health.get(PRIMARY_DATABASE, {}).get('available', False)
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        raise HTTPException(status_code=503, detail="Database health check failed")


@router.get("/health/detailed")
async def detailed_health() -> Dict[str, Any]:
    """
    Comprehensive health check with detailed system information.
    """
    try:
        db_health = get_database_health()
        
        # Calculate database statistics
        primary_healthy = db_health.get(PRIMARY_DATABASE, {}).get('available', False)
        backup_db = 'sqlite' if PRIMARY_DATABASE == 'postgresql' else 'postgresql'
        backup_healthy = db_health.get(backup_db, {}).get('available', False) if USE_DUAL_DATABASE else None
        
        # Determine system status
        if primary_healthy:
            system_status = "optimal"
        elif USE_DUAL_DATABASE and backup_healthy:
            system_status = "degraded"
        else:
            system_status = "critical"
        
        return {
            "timestamp": datetime.now().isoformat(),
            "system": {
                "status": system_status,
                "version": "1.0.0",
                "environment": "production"  # This could be read from config
            },
            "database": {
                "configuration": {
                    "primary": PRIMARY_DATABASE,
                    "dual_mode": USE_DUAL_DATABASE,
                    "backup": backup_db if USE_DUAL_DATABASE else None
                },
                "health": db_health,
                "status": {
                    "primary_available": primary_healthy,
                    "backup_available": backup_healthy,
                    "redundancy": "active" if USE_DUAL_DATABASE and backup_healthy else "none"
                }
            },
            "features": {
                "ocr_processing": True,
                "multi_supplier": True,
                "real_time_progress": True,
                "dual_database": USE_DUAL_DATABASE,
                "api_authentication": True
            },
            "recommendations": _get_health_recommendations(db_health, primary_healthy, backup_healthy)
        }
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {e}")
        raise HTTPException(status_code=503, detail="Detailed health check failed")


def _get_health_recommendations(db_health: Dict, primary_healthy: bool, backup_healthy: bool) -> list:
    """Generate health recommendations based on current status."""
    recommendations = []
    
    if not primary_healthy:
        recommendations.append({
            "level": "critical",
            "message": f"Primary database ({PRIMARY_DATABASE}) is not available",
            "action": "Check database connection and credentials"
        })
    
    if USE_DUAL_DATABASE and not backup_healthy:
        backup_db = 'sqlite' if PRIMARY_DATABASE == 'postgresql' else 'postgresql'
        recommendations.append({
            "level": "warning",
            "message": f"Backup database ({backup_db}) is not available",
            "action": "Check backup database configuration"
        })
    
    if not USE_DUAL_DATABASE:
        recommendations.append({
            "level": "info",
            "message": "Dual database mode is disabled",
            "action": "Consider enabling dual database for redundancy"
        })
    
    # Check for specific database errors
    for db_name, db_status in db_health.items():
        if not db_status.get('available') and db_status.get('error'):
            recommendations.append({
                "level": "error",
                "message": f"{db_name.title()} database error: {db_status['error']}",
                "action": f"Investigate {db_name} database connectivity"
            })
    
    if not recommendations:
        recommendations.append({
            "level": "success",
            "message": "All systems are operating normally",
            "action": "No action required"
        })
    
    return recommendations
