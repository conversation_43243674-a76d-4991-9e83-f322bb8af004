import sqlite3
import json
from pathlib import Path
import logging
from typing import List, Dict, Optional

# Import the new dual database operations
from src.app.database.dual_operations import dual_db
from src.app.database.connection import init_db as init_databases

# Legacy database path for backward compatibility
DATABASE_NAME = Path(__file__).parents[2] / "database/ocr_document_grossiste.db"
logging.info(f"Database: {DATABASE_NAME}")

def get_db_connection():
    """Legacy function - maintained for backward compatibility."""
    conn = sqlite3.connect(DATABASE_NAME)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    """Initialize both PostgreSQL and SQLite databases."""
    try:
        init_databases()
        logging.info("Dual database initialization completed successfully")
    except Exception as e:
        logging.error(f"Database initialization failed: {e}")
        raise

def save_response_to_db(responses, id_user, tenant_code, tenant_id, random_id):
    """
    Save response data to both PostgreSQL and SQLite databases.
    Uses the new dual database operations layer.
    """
    try:
        id_bl = dual_db.save_response_to_db(responses, id_user, tenant_code, tenant_id, random_id)
        logging.info(f"Saved response data to dual databases with ID: {id_bl}")
        return id_bl
    except Exception as e:
        logging.error(f"Error in save_response_to_db: {e}")
        raise


def get_all_pre_bl_ocr(user_id: str, tenant_id: str):
    """
    Get all pre_bl_ocr records for a user and tenant from dual databases.
    Uses the new dual database operations layer with fallback support.
    """
    try:
        return dual_db.get_all_pre_bl_ocr(user_id, tenant_id)
    except Exception as e:
        logging.error(f"Error in get_all_pre_bl_ocr: {e}")
        raise


def get_pre_bl_ocr_by_id(id_bl: int, user_id: str, tenant_id: str):
    """
    Get a specific pre_bl_ocr record by ID from dual databases.
    Uses the new dual database operations layer with fallback support.
    """
    try:
        result = dual_db.get_pre_bl_ocr_by_id(id_bl, user_id, tenant_id)
        if result:
            logging.info(f"Retrieved record with ID: {id_bl}")
        return result
    except Exception as e:
        logging.error(f"Error in get_pre_bl_ocr_by_id: {e}")
        return None


def get_pre_bl_ocr_by_random_id(random_id: str, user_id: str, tenant_id: str):
    """
    Get a specific pre_bl_ocr record by random_id from dual databases.
    Uses the new dual database operations layer with fallback support.
    """
    try:
        result = dual_db.get_pre_bl_ocr_by_random_id(random_id, user_id, tenant_id)
        if result:
            logging.info(f"Retrieved record with random_id: {random_id}")
        return result
    except Exception as e:
        logging.error(f"Error in get_pre_bl_ocr_by_random_id: {e}")
        return None


def get_all_pre_bl_ocr_by_user(user_id: str):
    """
    Get all pre_bl_ocr records for a specific user (across all tenants).
    Note: This function is kept for backward compatibility but should be used carefully
    as it bypasses tenant isolation.
    """
    try:
        # For now, we'll use the SQLite fallback for this function
        # since it's not commonly used and bypasses tenant isolation
        conn = get_db_connection()
        c = conn.cursor()
        c.execute("SELECT * FROM pre_bl_ocr WHERE status in ('EN_COURS', 'VALIDER') AND ID_USER = ?", (user_id,))
        rows = c.fetchall()
        conn.close()

        results = []
        for row in rows:
            results.append({
                "ID_BL": row['ID_BL'],
                "Content": json.loads(row['Content']) if row['Content'] else None,
                "ID_USER": row['ID_USER'],
                "status": row['status'],
                "ID_TENANT": row['ID_TENANT'],
                "CODE_TENANT": row['CODE_TENANT'],
                "date": row['date'],
                "id_BL_origine": row['id_BL_origine'],
                "date_BL_origine": row['date_BL_origine'],
                "supplier_name": row['supplier_name'],
                "supplier_id": str(row['supplier_id']) if row['supplier_id'] else None,
                "random_id": row['random_id']
            })
        return results
    except Exception as e:
        logging.error(f"Error in get_all_pre_bl_ocr_by_user: {e}")
        return []


def update_bl_status(bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id):
    """
    Update BL status in both PostgreSQL and SQLite databases.
    Uses the new dual database operations layer.
    """
    try:
        return dual_db.update_bl_status(bl_id, new_status, id_BL_origine, date_BL_origine, supplier_name, supplier_id)
    except Exception as e:
        logging.error(f"Error in update_bl_status: {e}")
        return False


def update_bl_status_valider(bl_id: int, user_id: str, tenant_id: str):
    """
    Update BL status to 'VALIDER' in both PostgreSQL and SQLite databases.
    Uses the new dual database operations layer.
    """
    try:
        return dual_db.update_bl_status_valider(bl_id, user_id, tenant_id)
    except Exception as e:
        logging.error(f"Error in update_bl_status_valider: {e}")
        return False


def get_database_health():
    """
    Get health status of both databases.
    Returns a dictionary with the status of PostgreSQL and SQLite connections.
    """
    try:
        return dual_db.get_database_health()
    except Exception as e:
        logging.error(f"Error checking database health: {e}")
        return {
            'postgresql': {'available': False, 'error': str(e)},
            'sqlite': {'available': False, 'error': str(e)}
        }